import { ColumnType, TableHeader } from '../../../models';
import {Completeness} from "@creactives/models";

export enum SearchEvents {
    TOGGLE_REFRESH = 'TOGGLE_REFRESH',
    SEARCH = 'SEARCH',
    NEW_PROCESS = 'NEW_PROCESS',
    OPEN_DOWNLOAD_TEMPLATE = 'OPEN_DOWNLOAD_TEMPLATE'
}

export enum MassiveCreationProcessStatus {
    A_CREATION_STARTING = 'A_CREATION_STARTING',
    A_CREATION_FORMAL_FILE_VALIDATION = 'A_CREATION_FORMAL_FILE_VALIDATION',
    A_CREATION_EXTRACTING_DATA = 'A_CREATION_EXTRACTING_DATA',
    A_CREATION_VALIDATING_DATA = 'A_CREATION_VALIDATING_DATA',
    H_CREATION_FILE_VALIDATION = 'H_CREATION_FILE_VALIDATION',
    A_CREATION_VALIDATING_CATEGORY = 'A_CREATION_VALIDATING_CATEGORY',
    A_CREATION_VALIDATING_CATEGORY_SUMMARY = 'A_CREATION_VALIDATING_CATEGORY_SUMMARY',
    A_CREATION_VALIDATING_BASIC_DATA = 'A_CREATION_VALIDATING_BASIC_DATA',
    A_CREATION_VALIDATING_BASIC_DATA_SUMMARY = 'A_CREATION_VALIDATING_BASIC_DATA_SUMMARY',
    A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES = 'A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES',
    A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES_SUMMARY = 'A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES_SUMMARY',
    A_CREATION_VALIDATING_PLANTS = 'A_CREATION_VALIDATING_PLANTS',
    A_CREATION_VALIDATING_PLANTS_SUMMARY = 'A_CREATION_VALIDATING_PLANTS_SUMMARY',
    H_CREATION_CONFIRM_DATA = 'H_CREATION_CONFIRM_DATA',
    A_CREATION_CONFIRM_DATA = 'A_CREATION_CONFIRM_DATA',
    A_CREATION_GOLDEN_RECORD_PLANTS = 'A_CREATION_GOLDEN_RECORD_PLANTS',
    A_CREATION_DUPLICATES_CHECK = 'A_CREATION_DUPLICATES_CHECK',
    A_CREATION_PROCESS_GENERATION = 'A_CREATION_PROCESS_GENERATION',
    A_CREATION_WAITING_FOR_COMPLETITION = 'A_CREATION_WAITING_FOR_COMPLETITION',
    H_CREATION_COMPLETED = 'H_CREATION_COMPLETED',
    H_CREATION_ABANDONED = 'H_CREATION_ABANDONED',
    H_CREATION_TERMINATED = 'H_CREATION_TERMINATED',
}

export enum MassiveCreationStepEventType {
    SAVE = 'SAVE',
    CHANGED = 'CHANGED',
    NEXT = 'NEXT',
    EXPORT = 'EXPORT',
    VALIDATE = 'VALIDATE',
    ABANDON = 'ABANDON',
    REFRESH = 'REFRESH',
    REPLAY = 'REPLAY',
    SET_ERROR = 'SET_ERROR',
    RELOAD_FILTERED = 'RELOAD_FILTERED',
    DELETE = 'DELETE'
}


export enum MassiveCreationStatusType {

    IGNORED = 'IGNORED',
    CONFIRMED = 'CONFIRMED',
    INVALID = 'INVALID',
    NOT_CHANGED = 'NOT_CHANGED',
    NOT_EDITABLE = 'NOT_EDITABLE',
    VALIDATED = 'VALIDATED',
    DRAFT = 'DRAFT'

}

const tblHeaders: TableHeader[] = [
    {
        columnLabel: 'massiveCreation.list.table.columns.processId',
        columnType: ColumnType.TEXT,
        columnFieldName: 'processId',
        sortableColumn: false,
        headerClass: 'tb-col-id ellipsis ',
        rowCellClass: 'tb-col-id '
    },
    {
        columnLabel: 'massiveCreation.list.table.columns.uploadDate',
        columnType: ColumnType.DATE,
        columnFieldName: 'uploadDate',
        sortableColumn: false,
        headerClass: 'tb-col-date ',
        rowCellClass: 'tb-col-date '
    },
    {
        columnLabel: 'massiveCreation.list.table.columns.processSummary',
        columnType: ColumnType.CUSTOM,
        columnFieldName: 'fileName',
        customTemplateRef: 'processSummary',
        sortableColumn: false,
        headerClass: 'tb-col-summary ',
        rowCellClass: 'tb-col-summary ',
    },

    {
        columnLabel: 'massiveCreation.list.table.columns.currentStatus',
        columnType: ColumnType.TEXT_TRANSLATED,
        columnFieldName: 'currentStatus',
        sortableColumn: false,
        rowOptions: {
            translationPrefix: 'massiveCreation.status.'
        },
        headerClass: 'tb-col-user ',
        rowCellClass: 'tb-col-user '
    },
    {
        columnLabel: 'massiveCreation.list.table.columns.actions',
        columnType: ColumnType.DEFAULT_ACTIONS,
        headerClass: 'tb-col-actions',
        rowCellClass: 'tb-col-actions',
        customTemplateRef: 'actions',
        rowOptions: {
            actions: {
                detail: true
            }
        },
        sortableColumn: false
    },
];

const processSteps = [
    { id: 'A_CREATION_STARTING', label: 'massiveCreation.status.A_CREATION_STARTING', icon: 'pi pi-server', visible: true },
    { id: 'A_CREATION_FORMAL_FILE_VALIDATION', label: 'massiveCreation.status.A_CREATION_FORMAL_FILE_VALIDATION', icon: 'pi pi-server', visible: true },
    { id: 'H_CREATION_FILE_VALIDATION', label: 'massiveCreation.status.H_CREATION_FILE_VALIDATION', icon: 'pi pi-server', visible: true },
    { id: 'A_CREATION_EXTRACTING_DATA', label: 'massiveCreation.status.A_CREATION_EXTRACTING_DATA', icon: 'pi pi-server', visible: true },
    { id: 'A_CREATION_VALIDATING_DATA', label: 'massiveCreation.status.A_CREATION_VALIDATING_DATA', icon: 'pi pi-server' },
    { id: 'A_CREATION_VALIDATING_CATEGORY', label: 'massiveCreation.status.A_CREATION_VALIDATING_CATEGORY', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_VALIDATING_CATEGORY_SUMMARY', label: 'massiveCreation.status.A_CREATION_VALIDATING_CATEGORY_SUMMARY', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_VALIDATING_BASIC_DATA', label: 'massiveCreation.status.A_CREATION_VALIDATING_BASIC_DATA', icon: 'pi pi-server', visible: true },
    { id: 'A_CREATION_VALIDATING_BASIC_DATA_SUMMARY', label: 'massiveCreation.status.A_CREATION_VALIDATING_BASIC_DATA_SUMMARY', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES', label: 'massiveCreation.status.A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES', icon: 'pi pi-server', visible: true },
    { id: 'A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES_SUMMARY', label: 'massiveCreation.status.A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES_SUMMARY', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_VALIDATING_PLANTS', label: 'massiveCreation.status.A_CREATION_VALIDATING_PLANTS', icon: 'pi pi-server', visible: true },
    { id: 'A_CREATION_VALIDATING_PLANTS_SUMMARY', label: 'massiveCreation.status.A_CREATION_VALIDATING_PLANTS_SUMMARY', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_GOLDEN_RECORD_PLANTS', label: 'massiveCreation.status.A_CREATION_GOLDEN_RECORD_PLANTS', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_DUPLICATES_CHECK', label: 'massiveCreation.status.A_CREATION_DUPLICATES_CHECK', icon: 'pi pi-server', visible: true },
    { id: 'H_CREATION_CONFIRM_DATA', label: 'massiveCreation.status.H_CREATION_CONFIRM_DATA', icon: 'pi pi-user', visible: false },
    { id: 'A_CREATION_CONFIRM_DATA', label: 'massiveCreation.status.A_CREATION_CONFIRM_DATA', icon: 'pi pi-user', visible: false },
    { id: 'A_CREATION_PROCESS_GENERATION', label: 'massiveCreation.status.A_CREATION_PROCESS_GENERATION', icon: 'pi pi-server', visible: false },
    { id: 'A_CREATION_WAITING_FOR_COMPLETITION', label: 'massiveCreation.status.A_CREATION_WAITING_FOR_COMPLETITION', icon: 'pi pi-server', visible: false },
    { id: 'H_CREATION_COMPLETED', label: 'massiveCreation.status.H_CREATION_COMPLETED', icon: 'pi pi-user', visible: true },
    { id: 'H_CREATION_ABANDONED', label: 'massiveCreation.status.H_CREATION_ABANDONED', icon: 'pi pi-user', visible: false },
    { id: 'H_CREATION_TERMINATED', label: 'massiveCreation.status.H_CREATION_TERMINATED', icon: 'pi pi-user', visible: false }
];


const tblFieldHeaders: TableHeader [] = [
    {
        columnLabel: 'massiveCreation.processDetail.steps.H_CREATION_CONFIRM_DATA.fieldTable.fieldName',
        columnType: ColumnType.TEXT_TRANSLATED,
        columnFieldName: 'ontologyFieldName',
        sortableColumn: true,
        headerClass: 'tb-col-field-name ellipsis ',
        rowCellClass: 'tb-col-field-name ',
        rowOptions: {
            translationPipeName: 'attributeName'
        }
    },
    {
        columnLabel: 'massiveCreation.processDetail.steps.H_CREATION_CONFIRM_DATA.fieldTable.changes',
        columnType: ColumnType.CUSTOM,
        sortableColumn: false,
        customTemplateRef: 'valueCell',
        headerClass: 'tb-col-value ellipsis ',
        rowCellClass: 'tb-col-value ',
    },
    {
        columnLabel: 'massiveCreation.processDetail.steps.H_CREATION_CONFIRM_DATA.fieldTable.status',
        columnType: ColumnType.CUSTOM,
        columnFieldName: 'status',
        sortableColumn: false,
        customTemplateRef: 'statusCell',
        headerClass: 'tb-col-status ellipsis ',
        rowCellClass: 'tb-col-status ',
    }
];

export const MASSIVE_CREATION_CONST = {
    tblHeaders,
    tblFieldHeaders,
    processSteps
};

export interface FieldDetailsRequest {
    fields: any;
    language: string;
    fallbackLanguages: string[];
}

export interface MaterialDetailsRequest {
    materialCode: string;
    client: string;
    language: string;
    fallbackLanguages: string[];
    page: string;
}

export interface NavigationTab {
  value?: number;
  key: string;
  active: boolean;
  type: string;
}

export interface MassiveStepFilters {
  filters: Record<string, any>;
  page: number;
  pageSize: number;
}

function markAsAlert(data: any[]): any[] {
    if (Array.isArray(data)) {
        return data[0];
    }
    return data;
}

export interface MassiveProcessFieldErrors {
  id: number;
  errorLevel: string;
  errorType: string;
  errorCategory: string;
}

export interface MassiveProcessFields {
    id: number;
    massiveProcessDataId: number;
    fieldName: string;
    newValue: string;
    valueType: string;
    oldValue: string;
    oldAttributeUnitOfMeasure: string;
    attributeUnitOfMeasure: string;
    statusType: string;
    ontologyFieldName: string;
    source: string;
    errors: MassiveProcessFieldErrors[];
}

export interface MassiveProcessCreationData {
  id: number;
  status: string;
  candidateMasterdataId: string;
  type: string;
  note: string;
  rowNumber: number;
  completeness: Completeness;
  wfProcessCode: string;
  wfProcessStatus: string;
  rejectNotes: string;
  rejectCategory: string;
  massiveProcessFields: MassiveProcessFields[];
}

export interface ConfigurationStepFilterType {
  name: string;
  type: string;
  values: { [key: string]: any };
}

export interface MassiveCreationStepInformation {
  data: MassiveProcessCreationData[];
  metadata: { [key: string]: any };
  filterTypes: ConfigurationStepFilterType[];
  pageInformation: StepPageInformation;
}

export interface StepPageInformation {
   page: number;
   pageSize: number;
   totalElements: number;
}

interface StepFilters {
    [key: string]: any;
}

export function updateNavigationTabsActiveState(
    navigationTabs: NavigationTab[] | null | undefined,
    stepFilters: StepFilters | null | undefined
): NavigationTab[] {
    const currentTabFilter: string = (stepFilters?.tabFilter ?? "BLOCKING_ERRORS")
        .toString()
        .trim();

    return navigationTabs?.map(tab => ({
        ...tab,
        active:
            tab?.key?.toString().trim() === currentTabFilter ||
            tab?.type?.toString().trim() === currentTabFilter,
    })) ?? [];
}

function getStatusIcon(status: MassiveCreationStatusType): string {
    switch (status) {
        case MassiveCreationStatusType.NOT_CHANGED:
            return 'fa-solid fa-equals';
        case MassiveCreationStatusType.INVALID:
            return 'fa-solid fa-triangle-exclamation';
        case MassiveCreationStatusType.VALIDATED:
            return 'fa-solid fa-question';
        case MassiveCreationStatusType.NOT_EDITABLE:
            return 'fa-solid fa-pencil-slash';
        case MassiveCreationStatusType.DRAFT:
            return 'fa-solid fa-gear';
        case MassiveCreationStatusType.IGNORED:
            return 'fa-solid fa-ban';
        case MassiveCreationStatusType.CONFIRMED:
            return 'fa-solid fa-check';
        default:
            return 'fa-solid fa-question';
    }
}

function getStatusColor(status: MassiveCreationStatusType): string {
    switch (status) {
        case MassiveCreationStatusType.NOT_CHANGED:
        case MassiveCreationStatusType.INVALID:
        case MassiveCreationStatusType.VALIDATED:
        case MassiveCreationStatusType.NOT_EDITABLE:
        case MassiveCreationStatusType.DRAFT:
        case MassiveCreationStatusType.IGNORED:
        case MassiveCreationStatusType.CONFIRMED:
            return 'bg-' + status;
        default:
            return 'bg-transparent';
    }
}

function showValueDiff(field: any, isNotEditable: boolean): boolean {
    const statusForChanges: boolean = field?.statusType !== MassiveCreationStatusType.NOT_CHANGED && !isNotEditable;

    const hasDifferenceInValue = (field?.newValue !== field?.oldValue)
        || (field?.newAttributeUnitOfMeasure !== field?.oldAttributeUnitOfMeasure);

    return statusForChanges && hasDifferenceInValue;
}

export const MassiveCreationUtils = {
    getStepDetail: markAsAlert,
    getStatusIcon,
    showValueDiff,
    getStatusColor
};

