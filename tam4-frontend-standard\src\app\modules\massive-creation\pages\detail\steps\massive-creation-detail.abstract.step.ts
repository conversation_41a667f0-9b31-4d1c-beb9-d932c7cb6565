import {Directive, EventEmitter, input, Input, OnDestroy, SimpleChanges} from '@angular/core';
import {Tam4ComponentEvent} from '../../../../../models';
import {EventUtils} from '../../../../../utils';
import {Subject} from 'rxjs';
import {MassiveCreationProcessStatus, MassiveCreationStepEventType, NavigationTab} from '../../../models/massive-creation.types';


export interface MassiveCreationDetailStep {
    stepData?: any | any[];
    stepEvent?: EventEmitter<Tam4ComponentEvent<any, any>>;
}

@Directive()
export class MassiveCreationDetailAbstractStep implements MassiveCreationDetailStep, OnDestroy {

    @Input()
    stepData: any | any = null;

    @Input()
    stepInfo: any | any[] = null;

    @Input()
    processInfo: any = null;

    navigationTabs = input<NavigationTab[] | null>(null);

    @Input()
    stepEvent?: EventEmitter<Tam4ComponentEvent<MassiveCreationStepEventType, any>>;

    @Input()
    stepType?: string;

    @Input()
    changed = false;

    @Input()
    loading = false;

    @Input()
    waitingNextStatus = false;

    readonly StepTypes = MassiveCreationProcessStatus;

    ngOnDestroy$: Subject<void> = new Subject<void>();

    constructor() {
    }

    ngOnInit() {
    }

    ngOnChanges(changes: SimpleChanges) {
    }

    ngOnDestroy() {
        this.ngOnDestroy$.next();
        this.ngOnDestroy$.complete();
    }

    doSave(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.SAVE,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doNext(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.NEXT,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doAbandon(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.ABANDON,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doReload(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.REFRESH,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doReplay(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.REPLAY,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doSetError(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.SET_ERROR,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doExport(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.EXPORT,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doChanged(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.CHANGED,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

    doReloadFiltered(event$: any, payload?: any | any[]) {
        EventUtils.stopPropagation(event$);
        this.stepEvent.emit({
            type: MassiveCreationStepEventType.RELOAD_FILTERED,
            subtype: this.stepType,
            nativeEvent: event$,
            payload
        });
    }

}
