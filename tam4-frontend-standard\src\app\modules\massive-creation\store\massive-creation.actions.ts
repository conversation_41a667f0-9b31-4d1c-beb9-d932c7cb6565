import {
  CommonFileDownloadWrapper,
  <PERSON>R<PERSON>ponseWrapper,
  PagedResponseWrapper,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ilter
} from '../../../models';
import { ReduxUtils } from '../../../utils';
import { MassiveCreationState, TAM_MASSIVE_CREATION_FEATURE_NAME } from './massive-creation.state';
import { MassiveProcessCreationData } from "../models/massive-creation.types";

export interface MassiveCreationProcessActionPayload<T> {
    processId: string;
    data?: T;
}

export const MASSIVE_CREATION_ACTION_NAMES = {
    INIT: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, '_generic_', 'INIT'),

    SEARCH: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'LIST', 'SEARCH'),
    SEARCH_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'LIST', 'SEARCH', ['SUCCESS']),
    SEARCH_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'LIST', 'SEARCH', ['FAILURE']),

    SEARCH_INIT: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'SEARCH', 'INIT'),
    SEARCH_INIT_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'SEARCH', 'INIT', ['SUCCESS']),
    SEARCH_INIT_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'SEARCH', 'INIT', ['FAILURE']),
    SEARCH_FORM_UPDATE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'SEARCH', 'FORM_UPDATE'),

    NEW_PROCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'NEW_PROCESS', 'INIT'),
    NEW_PROCESS_CLOSED: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'NEW_PROCESS', 'CLOSED'),
    NEW_PROCESS_SUBMIT: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME, 'NEW_PROCESS', 'SUBMIT'),
    NEW_PROCESS_SUBMIT_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'NEW_PROCESS',
        'SUBMIT',
        ['SUCCESS']),
    NEW_PROCESS_SUBMIT_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'NEW_PROCESS',
        'SUBMIT',
        ['FAILURE']),
    // PROCESS DETAIL
    PROCESS_DETAIL: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        '_generic_',
        'PROCESS_DETAIL'),
    PROCESS_DETAIL_CHANGED: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'CHANGED'),
    PROCESS_DETAIL_LOAD: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'LOAD'),
    PROCESS_DETAIL_RELOAD: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'RELOAD'),
    PROCESS_DETAIL_RELOAD_FILTERED: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'RELOAD_FILTERED'),
    PROCESS_DETAIL_RELOAD_FILTERED_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'RELOAD_FILTERED', ['SUCCESS']),
    PROCESS_DETAIL_RELOAD_FILTERED_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'RELOAD_FILTERED', ['FAILURE']),
    PROCESS_DETAIL_LOAD_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'LOAD',
        ['SUCCESS']),
    PROCESS_DETAIL_LOAD_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'LOAD',
        ['FAILURE']),

    PROCESS_DETAIL_VALIDATE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'VALIDATE'),
    PROCESS_DETAIL_VALIDATE_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'VALIDATE', ['SUCCESS']),
    PROCESS_DETAIL_VALIDATE_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'VALIDATE', ['FAILURE']),

    PROCESS_DETAIL_NEXTSTEP: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'NEXTSTEP'),
    PROCESS_DETAIL_NEXTSTEP_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'NEXTSTEP', ['SUCCESS']),
    PROCESS_DETAIL_NEXTSTEP_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'NEXTSTEP', ['FAILURE']),

    PROCESS_DETAIL_SAVE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'SAVE'),
    PROCESS_DETAIL_SAVE_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'SAVE', ['SUCCESS']),
    PROCESS_DETAIL_SAVE_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'SAVE', ['FAILURE']),

    PROCESS_DETAIL_ABANDON: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'ABANDON'),
    PROCESS_DETAIL_ABANDON_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'ABANDON', ['SUCCESS']),
    PROCESS_DETAIL_ABANDON_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'ABANDON', ['FAILURE']),

    PROCESS_DETAIL_EXPORT: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'EXPORT'),
    PROCESS_DETAIL_EXPORT_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'EXPORT', ['SUCCESS']),
    PROCESS_DETAIL_EXPORT_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'EXPORT', ['FAILURE']),

    PROCESS_DETAIL_REPLAY: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'REPLAY'),
    PROCESS_DETAIL_REPLAY_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'REPLAY', ['SUCCESS']),
    PROCESS_DETAIL_REPLAY_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'REPLAY', ['FAILURE']),

    PROCESS_DETAIL_SET_ERROR: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'SET_ERROR'),
    PROCESS_DETAIL_SET_ERROR_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'SET_ERROR', ['SUCCESS']),
    PROCESS_DETAIL_SET_ERROR_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'SET_ERROR', ['FAILURE']),

    PROCESS_DETAIL_DELETE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'DELETE'),
    PROCESS_DETAIL_DELETE_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'DELETE', ['SUCCESS']),
    PROCESS_DETAIL_DELETE_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'DELETE', ['FAILURE']),

    DOWNLOAD_TEMPLATE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'DOWNLOAD_TEMPLATE'),
    DOWNLOAD_TEMPLATE_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'DOWNLOAD_TEMPLATE', ['SUCCESS']),
    DOWNLOAD_TEMPLATE_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'DOWNLOAD_TEMPLATE', ['FAILURE']),
    GET_MATERIAL_DETAILS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'GET_MATERIAL_DETAILS'),
    GET_MATERIAL_DETAILS_SUCCESS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'GET_MATERIAL_DETAILS', ['SUCCESS']),
    GET_MATERIAL_DETAILS_FAILURE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'GET_MATERIAL_DETAILS', ['FAILURE']),
    OPEN_MATERIAL_DETAILS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'PROCESS_DETAIL',
        'OPEN_MATERIAL_DETAILS'),
    SET_USER_ACTIONS: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'SEARCH',
        'SET_USER_ACTION_LEVEL'),
    OPEN_DOWNLOAD_TEMPLATE: ReduxUtils.generateActionName(TAM_MASSIVE_CREATION_FEATURE_NAME,
        'OPEN_DOWNLOAD_TEMPLATE',
        'OPEN')
};

// ########## action types ############
export type MassiveCreationAction_Init = Tam4Action<MassiveCreationState>;
export type MassiveCreationAction_Search = Tam4Action<any>;
export type MassiveCreationAction_Search_Success = Tam4Action<CommonResponseWrapper<any[]>>;
export type MassiveCreationAction_Search_Failure = Tam4Action<CommonResponseWrapper<any>>;
export type MassiveCreationAction_Search_Init = Tam4Action<MassiveCreationState>;
export type MassiveCreationAction_Search_Init_Success = Tam4Action<CommonResponseWrapper<TamQueryFilter<any>[]>>;
export type MassiveCreationAction_Search_Init_Failure = Tam4Action<CommonResponseWrapper<any>>;
export type MassiveCreationAction_Search_FormUpdate = Tam4Action<MassiveCreationState>;

export type MassiveCreationAction_NewProcess = Tam4Action<MassiveCreationState>;
export type MassiveCreationAction_NewProcess_Closed = Tam4Action<MassiveCreationState>;
export type MassiveCreationAction_NewProcess_Submit = Tam4Action<File[]>;
export type MassiveCreationAction_NewProcess_Submit_Success = Tam4Action<CommonResponseWrapper<any>>;
export type MassiveCreationAction_NewProcess_Submit_Failure = Tam4Action<CommonResponseWrapper<any>>;

export type MassiveCreationAction_GetMaterialDetails = Tam4Action<any>;
export type MassiveCreationAction_GetMaterialDetails_Success = Tam4Action<any>;
export type MassiveCreationAction_GetMaterialDetails_Failure = Tam4Action<any>;

export type MassiveCreationAction_OpenMaterialDetails = Tam4Action<any>;

export type MassiveCreationAction_SetUserActions = Tam4Action<any>;

export type MassiveCreationAction_ProcessDetail = Tam4Action<MassiveCreationProcessActionPayload<void>>;
export type MassiveCreationAction_ProcessDetail_Changed = Tam4Action<any>;
export type MassiveCreationAction_ProcessDetail_Load = Tam4Action<MassiveCreationProcessActionPayload<void>>;
export type MassiveCreationAction_ProcessDetail_Reload = Tam4Action<MassiveCreationProcessActionPayload<void>>;
export type MassiveCreationAction_ProcessDetail_Reload_Filtered = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Reload_Filtered_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Reload_Filtered_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Load_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Load_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_Validate = Tam4Action<MassiveCreationProcessActionPayload<void>>;
export type MassiveCreationAction_ProcessDetail_Validate_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Validate_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_NextStep = Tam4Action<MassiveCreationProcessActionPayload<void>>;
export type MassiveCreationAction_ProcessDetail_NextStep_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_NextStep_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_Save = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Save_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Save_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_Abandon = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Abandon_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Abandon_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;


export type MassiveCreationAction_ProcessDetail_Export = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Export_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonFileDownloadWrapper>>;
export type MassiveCreationAction_ProcessDetail_Export_Failure = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_Replay = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Replay_Success = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Replay_Failure =
    Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_Set_Error = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Set_Error_Success = Tam4Action<MassiveCreationProcessActionPayload<any>>;
export type MassiveCreationAction_ProcessDetail_Set_Error_Failure =
    Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_ProcessDetail_Delete = Tam4Action<MassiveCreationProcessActionPayload<MassiveProcessCreationData[]>>;
export type MassiveCreationAction_ProcessDetail_Delete_Success = Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;
export type MassiveCreationAction_ProcessDetail_Delete_Failure =
    Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_DownloadTemplate = Tam4Action<any>;
export type MassiveCreationAction_DownloadTemplate_Success =
    Tam4Action<MassiveCreationProcessActionPayload<{ file: Blob, fileName: string }>>;
export type MassiveCreationAction_DownloadTemplate_Failure =
    Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;

export type MassiveCreationAction_OpenDownloadTemplate_Request =
    Tam4Action<MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>;


// ---------------------------
export type MassiveCreationActionTypes = MassiveCreationAction_Init
    | MassiveCreationAction_Search
    | MassiveCreationAction_Search_Success
    | MassiveCreationAction_Search_Failure
    | MassiveCreationAction_Search_Init
    | MassiveCreationAction_Search_Init_Success
    | MassiveCreationAction_Search_Init_Failure
    | MassiveCreationAction_Search_FormUpdate
    | MassiveCreationAction_NewProcess
    | MassiveCreationAction_NewProcess_Closed
    | MassiveCreationAction_NewProcess_Submit
    | MassiveCreationAction_NewProcess_Submit_Success
    | MassiveCreationAction_NewProcess_Submit_Failure
    | MassiveCreationAction_ProcessDetail
    | MassiveCreationAction_ProcessDetail_Changed
    | MassiveCreationAction_ProcessDetail_Load
    | MassiveCreationAction_ProcessDetail_Reload
    | MassiveCreationAction_ProcessDetail_Load_Success
    | MassiveCreationAction_ProcessDetail_Load_Failure
    | MassiveCreationAction_ProcessDetail_Validate
    | MassiveCreationAction_ProcessDetail_Validate_Success
    | MassiveCreationAction_ProcessDetail_Validate_Failure
    | MassiveCreationAction_ProcessDetail_NextStep
    | MassiveCreationAction_ProcessDetail_NextStep_Success
    | MassiveCreationAction_ProcessDetail_NextStep_Failure
    | MassiveCreationAction_ProcessDetail_Save
    | MassiveCreationAction_ProcessDetail_Save_Success
    | MassiveCreationAction_ProcessDetail_Save_Failure
    | MassiveCreationAction_ProcessDetail_Abandon
    | MassiveCreationAction_ProcessDetail_Abandon_Success
    | MassiveCreationAction_ProcessDetail_Abandon_Failure
    | MassiveCreationAction_ProcessDetail_Export
    | MassiveCreationAction_ProcessDetail_Export_Success
    | MassiveCreationAction_ProcessDetail_Export_Failure
    | MassiveCreationAction_ProcessDetail_Replay
    | MassiveCreationAction_ProcessDetail_Replay_Success
    | MassiveCreationAction_ProcessDetail_Replay_Failure
    | MassiveCreationAction_ProcessDetail_Set_Error
    | MassiveCreationAction_ProcessDetail_Set_Error_Success
    | MassiveCreationAction_ProcessDetail_Set_Error_Failure
    | MassiveCreationAction_ProcessDetail_Delete
    | MassiveCreationAction_ProcessDetail_Delete_Success
    | MassiveCreationAction_ProcessDetail_Delete_Failure
    | MassiveCreationAction_DownloadTemplate
    | MassiveCreationAction_DownloadTemplate_Success
    | MassiveCreationAction_DownloadTemplate_Failure
    ;

// ########### ACTION GENERATOR ##############
export const MASSIVE_CREATION_ACTIONS = {
    INIT: ReduxUtils.generateTypedAction<MassiveCreationAction_Init, MassiveCreationState>(MASSIVE_CREATION_ACTION_NAMES.INIT),
    SEARCH: ReduxUtils.generateTypedAction<MassiveCreationAction_Search, any>(MASSIVE_CREATION_ACTION_NAMES.SEARCH),
    SEARCH_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_Search_Success, PagedResponseWrapper<any>>(
        MASSIVE_CREATION_ACTION_NAMES.SEARCH_SUCCESS),
    SEARCH_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_Search_Failure, PagedResponseWrapper<any>>(
        MASSIVE_CREATION_ACTION_NAMES.SEARCH_FAILURE),

    SEARCH_INIT: ReduxUtils.generateTypedAction<MassiveCreationAction_Search_Init, MassiveCreationState>(
        MASSIVE_CREATION_ACTION_NAMES.SEARCH_INIT),
    SEARCH_INIT_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_Search_Init_Success, CommonResponseWrapper<TamQueryFilter<any>[]>>(
        MASSIVE_CREATION_ACTION_NAMES.SEARCH_INIT_SUCCESS),
    SEARCH_INIT_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_Search_Init_Failure, CommonResponseWrapper<any>>(
        MASSIVE_CREATION_ACTION_NAMES.SEARCH_INIT_FAILURE),
    SEARCH_FORM_UPDATE: ReduxUtils.generateTypedAction<MassiveCreationAction_Search_FormUpdate, MassiveCreationState>(
        MASSIVE_CREATION_ACTION_NAMES.SEARCH_FORM_UPDATE),

    NEW_PROCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_NewProcess, any>(MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS),
    NEW_PROCESS_CLOSED: ReduxUtils.generateTypedAction<MassiveCreationAction_NewProcess_Closed, any>(
        MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_CLOSED),
    NEW_PROCESS_SUBMIT: ReduxUtils.generateTypedAction<MassiveCreationAction_NewProcess_Submit, any>(
        MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT),
    NEW_PROCESS_SUBMIT_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_NewProcess_Submit_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT_SUCCESS),
    NEW_PROCESS_SUBMIT_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_NewProcess_Submit_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT_FAILURE),

    PROCESS_DETAIL: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL),
    PROCESS_DETAIL_CHANGED: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Changed, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_CHANGED),
    PROCESS_DETAIL_LOAD: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Load, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD),
    PROCESS_DETAIL_RELOAD: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Reload, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD),
    PROCESS_DETAIL_RELOAD_FILTERED: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Reload_Filtered, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED),
    PROCESS_DETAIL_RELOAD_FILTERED_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Reload_Filtered_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED_SUCCESS),
    PROCESS_DETAIL_RELOAD_FILTERED_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Reload_Filtered_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED_FAILURE),
    PROCESS_DETAIL_LOAD_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Load_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD_SUCCESS),
    PROCESS_DETAIL_LOAD_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Load_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD_FAILURE),

    PROCESS_DETAIL_VALIDATE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Validate, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_VALIDATE),
    PROCESS_DETAIL_VALIDATE_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Validate_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_VALIDATE_SUCCESS),
    PROCESS_DETAIL_VALIDATE_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Validate_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_VALIDATE_FAILURE),

    PROCESS_DETAIL_NEXTSTEP: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_NextStep, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_NEXTSTEP),
    PROCESS_DETAIL_NEXTSTEP_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_NextStep_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_NEXTSTEP_SUCCESS),
    PROCESS_DETAIL_NEXTSTEP_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_NextStep_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_NEXTSTEP_FAILURE),

    PROCESS_DETAIL_SAVE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Save, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE),
    PROCESS_DETAIL_SAVE_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Save_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE_SUCCESS),
    PROCESS_DETAIL_SAVE_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Save_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE_FAILURE),

    PROCESS_DETAIL_ABANDON: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Abandon, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_ABANDON),
    PROCESS_DETAIL_ABANDON_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Abandon_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_ABANDON_SUCCESS),
    PROCESS_DETAIL_ABANDON_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Abandon_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_ABANDON_FAILURE),

    PROCESS_DETAIL_EXPORT: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Export, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_EXPORT),
    PROCESS_DETAIL_EXPORT_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Export_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_EXPORT_SUCCESS),
    PROCESS_DETAIL_EXPORT_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Export_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_EXPORT_FAILURE),

    PROCESS_DETAIL_REPLAY: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Replay, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_REPLAY),
    PROCESS_DETAIL_REPLAY_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Replay_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_REPLAY_SUCCESS),
    PROCESS_DETAIL_REPLAY_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Replay_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_REPLAY_FAILURE),

    PROCESS_DETAIL_SET_ERROR: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Set_Error, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SET_ERROR),
    PROCESS_DETAIL_SET_ERROR_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Set_Error_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SET_ERROR_SUCCESS),
    PROCESS_DETAIL_SET_ERROR_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Set_Error_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SET_ERROR_FAILURE),

    PROCESS_DETAIL_DELETE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Delete, MassiveCreationProcessActionPayload<MassiveProcessCreationData[]>>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_DELETE),
    PROCESS_DETAIL_DELETE_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Delete_Success, MassiveCreationProcessActionPayload<CommonResponseWrapper<any>>>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_DELETE_SUCCESS),
    PROCESS_DETAIL_DELETE_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_ProcessDetail_Delete_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_DELETE_FAILURE),

    DOWNLOAD_TEMPLATE: ReduxUtils.generateTypedAction<MassiveCreationAction_DownloadTemplate, any>(
        MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE),
    DOWNLOAD_TEMPLATE_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_DownloadTemplate_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE_SUCCESS),
    DOWNLOAD_TEMPLATE_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_DownloadTemplate_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE_FAILURE),

    OPEN_DOWNLOAD_TEMPLATE: ReduxUtils.generateTypedAction<MassiveCreationAction_DownloadTemplate, any>(
        MASSIVE_CREATION_ACTION_NAMES.OPEN_DOWNLOAD_TEMPLATE),

    GET_MATERIAL_DETAILS: ReduxUtils.generateTypedAction<MassiveCreationAction_GetMaterialDetails, any>(
        MASSIVE_CREATION_ACTION_NAMES.GET_MATERIAL_DETAILS),
    GET_MATERIAL_DETAILS_SUCCESS: ReduxUtils.generateTypedAction<MassiveCreationAction_GetMaterialDetails_Success, any>(
        MASSIVE_CREATION_ACTION_NAMES.GET_MATERIAL_DETAILS_SUCCESS),
    GET_MATERIAL_DETAILS_FAILURE: ReduxUtils.generateTypedAction<MassiveCreationAction_GetMaterialDetails_Failure, any>(
        MASSIVE_CREATION_ACTION_NAMES.GET_MATERIAL_DETAILS_FAILURE),

    OPEN_MATERIAL_DETAILS: ReduxUtils.generateTypedAction<MassiveCreationAction_OpenMaterialDetails, any>(
        MASSIVE_CREATION_ACTION_NAMES.OPEN_MATERIAL_DETAILS),

    SET_USER_ACTIONS: ReduxUtils.generateTypedAction<MassiveCreationAction_SetUserActions, any>(
        MASSIVE_CREATION_ACTION_NAMES.SET_USER_ACTIONS)

};
