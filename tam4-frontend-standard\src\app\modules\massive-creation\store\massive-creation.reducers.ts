import {
    MassiveCreationState,
    TAM_MASSIVE_CREATION_INITIAL_STATE
} from './massive-creation.state';
import {
    MASSIVE_CREATION_ACTION_NAMES,
    MassiveCreationAction_ProcessDetail_Load_Success,
    MassiveCreationAction_ProcessDetail_Save,
    MassiveCreationAction_Search_Init_Failure,
    MassiveCreationAction_Search_Init_Success,
    MassiveCreationAction_Search_Success,
    MassiveCreationAction_SetUserActions,
    MassiveCreationActionTypes
} from './massive-creation.actions';
import {ObjectsUtils} from 'src/app/utils';
import {MassiveCreationUtils, updateNavigationTabsActiveState} from '../models/massive-creation.types';

export class MassiveCreationReducers {
    constructor() {
    }

    public static reduce(state: MassiveCreationState = TAM_MASSIVE_CREATION_INITIAL_STATE,
                         action: MassiveCreationActionTypes = {type: null}): MassiveCreationState {
        switch (action.type) {
            case MASSIVE_CREATION_ACTION_NAMES.SEARCH_INIT_SUCCESS:
                return MassiveCreationReducers.doSearchInitSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.SEARCH:
                return MassiveCreationReducers.doSearch(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.SEARCH_SUCCESS:
                return MassiveCreationReducers.doSearchSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS:
                return MassiveCreationReducers.doNewProcess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT:
                return MassiveCreationReducers.doNewProcessSubmit(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT_SUCCESS:
                return MassiveCreationReducers.doNewProcessSubmitSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT_FAILURE:
                return MassiveCreationReducers.doNewProcessSubmitFailure(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_CLOSED:
                return MassiveCreationReducers.doNewProcessClosed(state, action);

            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_CHANGED:
                return MassiveCreationReducers.doProcessDetailChanged(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD:
                return MassiveCreationReducers.doProcessDetailLoad(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD:
                return MassiveCreationReducers.doProcessDetailReload(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED:
                return MassiveCreationReducers.doProcessDetailReloadFiltered(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED_SUCCESS:
                return MassiveCreationReducers.doProcessDetailReloadFilteredSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED_FAILURE:
                return MassiveCreationReducers.doProcessDetailReloadFilteredFailure(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD_SUCCESS:
                return MassiveCreationReducers.doProcessDetailLoadSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD_FAILURE:
                return MassiveCreationReducers.doProcessDetailLoadFailure(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE:
                return MassiveCreationReducers.doProcessDetailSave(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE_SUCCESS:
                return MassiveCreationReducers.doProcessDetailSaveSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.GET_MATERIAL_DETAILS:
                return MassiveCreationReducers.doGetMaterialDetails(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.GET_MATERIAL_DETAILS_SUCCESS:
                return MassiveCreationReducers.doGetMaterialDetailsSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_NEXTSTEP_SUCCESS:
                return MassiveCreationReducers.doProcessDetailNextstepSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE:
                return MassiveCreationReducers.doDownloadTemplate(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE_SUCCESS:
                return MassiveCreationReducers.doDownloadTemplateSuccess(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE_FAILURE:
                return MassiveCreationReducers.doDownloadTemplateError(state, action);
            case MASSIVE_CREATION_ACTION_NAMES.SET_USER_ACTIONS:
                return MassiveCreationReducers.setUserActions(state, action);
            default:
                return ObjectsUtils.deepClone<MassiveCreationState>(state);
        }
    }

    public static doSearchInitSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_Search_Init_Success = ObjectsUtils.forceCast<MassiveCreationAction_Search_Init_Success>(
            action);

        newState.search.formConfig = a.payload?.data;

        return newState;
    }

    public static doNewProcess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.search.uploadModal = {
            visible: true
        };

        return newState;
    }

    public static doNewProcessSubmit(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.search.uploadModal.loading = true;

        return newState;
    }

    public static doNewProcessSubmitSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.search.uploadModal = null;

        return newState;
    }

    public static doNewProcessSubmitFailure(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_Search_Init_Failure = ObjectsUtils.forceCast<MassiveCreationAction_Search_Init_Failure>(
            action);
        newState.search.uploadModal.loading = false;
        newState.search.uploadModal.messages = a.payload.messages;

        return newState;
    }

    public static doNewProcessClosed(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.search.uploadModal = null;

        return newState;
    }

    public static doSearch(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.search.results = {
            loading: true,
        };

        return newState;
    }

    public static doSearchSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_Search_Success = ObjectsUtils.forceCast<MassiveCreationAction_Search_Success>(
            action);

        newState.search.results = {
            loading: false,
            data: a.payload.data
        };

        return newState;
    }

    public static doProcessDetailLoad(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {
            ...ObjectsUtils.deepClone<MassiveCreationState>(state), detail: {
                loading: true,
                reloading: true
            }
        };
        return newState;
    }

    public static doProcessDetailChanged(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.detail.changed = true;
        newState.detail.stepDetail = MassiveCreationUtils.getStepDetail(action?.payload);
        return newState;
    }


    public static doProcessDetailReload(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.detail.reloading = true;

        return newState;
    }

    public static doProcessDetailReloadFiltered(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.detail.reloading = true;
        newState.detail.stepFilters = action?.payload?.data || new Map();

        return newState;
    }

    public static doProcessDetailReloadFilteredSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_ProcessDetail_Load_Success = ObjectsUtils.forceCast<MassiveCreationAction_ProcessDetail_Load_Success>(
            action);

        const currStatus = newState?.detail?.processDetail?.currentStatus;
        const newStatus = a.payload.data?.data?.processDto?.currentStatus;

        // TODO: Review this!
        if (newState?.detail?.waitingNextStep && newStatus === currStatus) {
            // tslint:disable-next-line:no-console
        } else {
            const updatedNavigationTabs = updateNavigationTabsActiveState(
                a.payload.data?.data?.navigationTabs,
                newState.detail.stepFilters || { tabFilter: 'BLOCKING_ERRORS' }
            );

            newState.detail = {
                ...newState.detail,
                loading: false,
                reloading: false,
                changed: false,
                processDetail: a.payload.data?.data?.processDto,
                navigationTabs: updatedNavigationTabs,
                stepDetail: MassiveCreationUtils.getStepDetail(a.payload.data.data?.stepInformation)
            };
        }

        return newState;
    }

    public static doProcessDetailReloadFilteredFailure(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);

        newState.detail.loading = false;
        newState.detail.reloading = false;

        return newState;
    }

    public static doProcessDetailLoadSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_ProcessDetail_Load_Success =
            ObjectsUtils.forceCast<MassiveCreationAction_ProcessDetail_Load_Success>(action);

        const currStatus = newState?.detail?.processDetail?.currentStatus;
        const newStatus = a.payload.data?.data?.processDto?.currentStatus;

        // TODO: Review this!
        if (newState?.detail?.waitingNextStep && newStatus === currStatus) {
            // tslint:disable-next-line:no-console
        } else {
            const updatedNavigationTabs = updateNavigationTabsActiveState(
                a.payload.data?.data?.navigationTabs,
                newState.detail.stepFilters || { tabFilter: 'BLOCKING_ERRORS' }
            );

            newState.detail = {
                ...newState.detail,
                loading: false,
                reloading: false,
                changed: false,
                processDetail: a.payload.data?.data?.processDto,
                navigationTabs: updatedNavigationTabs,
                stepDetail: MassiveCreationUtils.getStepDetail(a.payload.data.data?.stepInformation)
            };
        }


        return newState;
    }

    public static doProcessDetailLoadFailure(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_ProcessDetail_Load_Success =
            ObjectsUtils.forceCast<MassiveCreationAction_ProcessDetail_Load_Success>(action);

        newState.detail = {
            loading: false,
            reloading: false,
        };

        return newState;
    }


    public static doProcessDetailSave(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_ProcessDetail_Save = ObjectsUtils.forceCast<MassiveCreationAction_ProcessDetail_Save>(
            action);

        newState.detail.reloading = true;
        newState.detail.stepDetail = action?.payload?.data;
        return newState;
    }


    public static doProcessDetailSaveSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {...ObjectsUtils.deepClone<MassiveCreationState>(state)};
        return newState;
    }

    public static doGetMaterialDetails(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_Search_Success = ObjectsUtils.forceCast<MassiveCreationAction_Search_Success>(
            action);

        newState.detail.materialDetails = null;

        return newState;
    }

    public static doGetMaterialDetailsSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = ObjectsUtils.deepClone<MassiveCreationState>(state);
        const a: MassiveCreationAction_Search_Success = ObjectsUtils.forceCast<MassiveCreationAction_Search_Success>(
            action);

        newState.detail.materialDetails = {
            ...a.payload
        };

        return newState;
    }

    public static doProcessDetailNextstepSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {...ObjectsUtils.deepClone<MassiveCreationState>(state)};

        newState.detail.waitingNextStep = true;
        newState.detail.reloading = true;

        return newState;
    }

    public static doDownloadTemplate(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {...ObjectsUtils.deepClone<MassiveCreationState>(state)};
        newState.download.loading = true;
        return newState;
    }

    public static doDownloadTemplateSuccess(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {...ObjectsUtils.deepClone<MassiveCreationState>(state)};
        newState.download.loading = false;
        return newState;
    }

    public static doDownloadTemplateError(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {...ObjectsUtils.deepClone<MassiveCreationState>(state)};
        newState.download.loading = false;
        return newState;
    }

    public static setUserActions(state: MassiveCreationState, action: MassiveCreationActionTypes) {
        const newState: MassiveCreationState = {...ObjectsUtils.deepClone<MassiveCreationState>(state)};
        const a: MassiveCreationAction_SetUserActions = ObjectsUtils.forceCast<MassiveCreationAction_SetUserActions>(
            action);

        newState.search.userActions = a.payload;
        return newState;
    }

}
