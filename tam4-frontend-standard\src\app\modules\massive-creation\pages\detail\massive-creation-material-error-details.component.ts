import { CommonModule } from "@angular/common";
import { Component, input } from "@angular/core";
import { TranslateModule } from "@ngx-translate/core";
import { TableModule } from "primeng/table";

@Component({
  selector: "div[materialErrorDetails]",
  template: `
    <div class="error-details">
      <h4 class="modal-title pull-left">
        {{
          "massiveCreation.validatingData.errors.details.rowNumber"
            | translate
        }}
        {{ fields().rowNumber }}
      </h4>
      <p-table class="tb-H_CREATION_CONFIRM_DATA_DETAILS" [value]="fields().fields">
        <ng-template pTemplate="header">
          <tr>
            <th scope="col">{{'massiveCreation.validatingData.errors.details.tableHeader.attribute' | translate}}</th>
            <th scope="col">{{'massiveCreation.validatingData.errors.details.tableHeader.value' | translate}}</th>
            <th scope="col">{{'massiveCreation.validatingData.errors.details.tableHeader.error' | translate}}</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-field>
          <tr [class.table-danger]="field.hasError">
            <td>{{'massiveCreation.fields.' + field.fieldName | translate}}</td>
            <td>{{field.newValue}}</td>
            <td>
              @if (fieldHasError(field)) {
                <ul>
                  @for (error of field.errors; track error) {
                    <li>
                      {{error.errorType}}
                    </li>
                  }
                </ul>
              }
          </tr>
        </ng-template>
      </p-table>
    </div>
  `,
  standalone: true,
  imports: [
    CommonModule, 
    TranslateModule,
    TableModule
  ],
})
export class MassiveCreationMaterialErrorDetailsComponent {
  
  fields = input<any>();

  fieldHasError(field: any) {
    return field.errors.length > 0;
  }
}
