import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { MassiveCreationMenuComponent } from './menu.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NavigationTab } from '../../models/massive-creation.types';

@Component({
  selector: 'div[navigationTab]',
  template: `
      @if (!loading) {
        <div class="route-ct m-portlet">
            <div class="row no-gutters route-link" (click)="changeTab()" [class.active-link]="navtab.active">
              <div menu [menuTitle]="'massiveCreation.validatingData.navigationHeader.' + navtab.key | translate" [indicator]="navtab.value">
              </div>
            </div>
        </div>
      } @else {
        <div class="col-md-4 route-ct">
            <div class="m-widget14">
                <div>
                    <h4 class="m-widget14__title">
                        <span class="m-widget14__desc skeleton-box col-3" ></span>
                        <span class="m-widget14__desc skeleton-box col-9"></span>
                    </h4>
                </div>
            </div>
        </div>
      }
  `,
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MassiveCreationMenuComponent
  ],
  styles: [`
      .route-ct {
        flex: 1;
        padding-left: 0;
        padding-right: 0;
        border-right: 1px solid #e6e0e0;

        > div {
          height: 100%;
        }

        .m-widget14 {
          padding: 1.5rem;
        }
      }

      .active-link {
        border-bottom: 5px solid #607D8B;
        text-decoration: none;
      }

      :focus {
        outline: none;
      }

      .skeleton-box {
        display: inline-block;
        height: 1em;
        position: relative;
        overflow: hidden;
        background-color: #DDDBDD;

        &::after {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          transform: translateX(-100%);
          background-image: linear-gradient(
                          90deg,
                          rgba(#fff, 0) 0,
                          rgba(#fff, 0.2) 20%,
                          rgba(#fff, 0.5) 60%,
                          rgba(#fff, 0)
          );
          animation: shimmer 2s infinite;
          content: '';
        }

        @keyframes shimmer {
          100% {
            transform: translateX(100%);
          }
        }
      }
  `],
  host: {
    '[style.display]': 'contents',
    '[style.position]': 'relative'
  }
})
export class MassiveCreationNavigationTabComponent implements OnInit {

  @Input()
  navtab: NavigationTab;
  @Input()
  loading: boolean;

  @Output()
  tabClick = new EventEmitter<any>();

  constructor() {}

  ngOnInit() {}

  changeTab() {
    this.tabClick.emit(this.navtab.key);
  }

}
