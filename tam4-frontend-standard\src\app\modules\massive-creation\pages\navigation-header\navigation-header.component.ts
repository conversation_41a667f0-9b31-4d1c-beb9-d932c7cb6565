import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { MassiveCreationNavigationTabComponent } from './navigation-tab.component';
import { CommonModule } from '@angular/common';
import { NavigationTab } from '../../models/massive-creation.types';

@Component({
  selector: 'div[navigationHeader]',
  template: `
    <div class="navigation-header">
      @for(tab of tabs; track tab) {
        <div class="navigation-tab-wrapper">
          <div navigationTab class="row no-gutters" [navtab]="tab"
               [activeTab]="tab.active" [loading]="loading"
               (tabClick)="tabChanged($event)">
          </div>
        </div>
      }
    </div>
  `,
  standalone: true,
  imports: [
    CommonModule,
    MassiveCreationNavigationTabComponent
  ],
  styles: [`
  .navigation-header {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: stretch;
    gap: 0;
    width: 100%;
  }

  .navigation-tab-wrapper {
    flex: 1 1 0;
    margin: 0;
    padding: 0;
  }

  [navigationTab] {
    display: block;
    width: 100%;
    height: 100%;
    margin: 0;
  }

  .navigation-header .row {
    margin-left: 0;
    margin-right: 0;
  }
`]
})
export class MassiveCreationNavigationHeaderComponent implements OnInit {

  @Input()
  tabs: Array<NavigationTab>;

  @Input()
  activeTab: any;

  @Input()
  loading = true;

  @Output()
  changeTab = new EventEmitter<any>();

  constructor() { }

  ngOnInit() {
  }

  tabChanged(e) {
    this.changeTab.emit(e);
  }

}
