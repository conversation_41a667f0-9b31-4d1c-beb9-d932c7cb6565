import {CommonMessageWrapper, GenericSearchForm, TableHeader} from '../../../models';

export const TAM_MASSIVE_CREATION_FEATURE_NAME = 'MASSIVE-CREATION';

// tslint:disable-next-line:no-empty-interface
export interface MassiveCreationSearchForm extends GenericSearchForm {
}

export interface MassiveCreationSearchResults {
    loading?: boolean;
    data?: any[];
}

export interface MassiveCreationSearchState {
    tableColumns?: TableHeader[];
    formConfig?: MassiveCreationSearchForm;
    userActions?: any;
    results?: MassiveCreationSearchResults;
    uploadModal?: {
        messages?: CommonMessageWrapper[];
        loading?: boolean;
        visible: boolean
    };
}

export interface MassiveCreationDetailState {
    loading?: boolean;
    reloading?: boolean;
    waitingNextStep?: boolean;
    processDetail?: any;
    stepDetail?: any;
    navigationTabs?: any;
    materialDetails?: any;
    changed?: boolean;
    stepFilters?: Record<string, any>;
}

export interface MassiveCreationState {
    search?: MassiveCreationSearchState;
    detail?: MassiveCreationDetailState;
    download?: MassiveCreationDownloadState;
}

export interface MassiveCreationDownloadState {
    loading?: boolean;
}

export const TAM_MASSIVE_CREATION_INITIAL_STATE: MassiveCreationState = {
    search: {},
    detail: {},
    download: {loading: false}
};
