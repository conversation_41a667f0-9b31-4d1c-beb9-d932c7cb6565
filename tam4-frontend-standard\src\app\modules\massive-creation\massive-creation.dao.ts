import { Injectable } from '@angular/core';
import { CommonFileDownloadWrapper, CommonResponseWrapper, PagedResponseWrapper, TamQueryFilter } from '../../models';
import { Tam4HttpClientService } from '../../providers';
import { MassiveCreationAction_DownloadTemplate } from "./store/massive-creation.actions";

const BASE_URL = `bulk-upload/api/massive-creation`;
const MATERIALS_URL = `materials/api`;
const URLS = {
    SEARCH: `${BASE_URL}/list`,
    SEARCH_INIT: `${BASE_URL}/list-filter-config`,
    PROCESS_START: `${BASE_URL}/process/start`,
    PROCESS_BASE_URL: `${BASE_URL}/process`,
    GET_MATERIAL_DETAILS: `${MATERIALS_URL}/get-details-for-massive-creation`,
    LOAD_MATERIAL_VIEW: `${MATERIALS_URL}/smart-details/get-details-by-client-and-code`,
    GET_MATERIAL_ERRORS: `${MATERIALS_URL}/masterdata-candidate`
};


@Injectable({
    providedIn: 'root'
})
export class MassiveCreationDao {
    constructor(private http: Tam4HttpClientService) {

    }

    search(searchForm: any,
           callbackOk: (resp: PagedResponseWrapper<any[]>) => void,
           callbackKo: (err: any) => void) {

        this.http.post<PagedResponseWrapper<any[]>>(URLS.SEARCH,
            searchForm,
            (resp: PagedResponseWrapper<any[]>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    searchInit(callbackOk: (resp: CommonResponseWrapper<TamQueryFilter<any>[]>) => void,
               callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<TamQueryFilter<any>[]>>(URLS.SEARCH_INIT,
            {},
            (resp: CommonResponseWrapper<TamQueryFilter<any>[]>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    startProcess(language: string, file: File,
                 callbackOk: (resp: CommonResponseWrapper<any>) => void,
                 callbackKo: (err: any) => void) {
        const formData: FormData = new FormData();
        formData.append('currentLanguage', language);
        formData.append('file', file, file.name);

        this.http.post<CommonResponseWrapper<any>>(URLS.PROCESS_START,
            formData,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    loadProcessDetail(processId: string,
                      callbackOk: (resp: CommonResponseWrapper<any>) => void,
                      callbackKo: (err: any) => void) {
        this.http.get<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}`,
            {},
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    reloadFilteredProcessDetail(processId: string,
                                filters: any,
                                callbackOk: (resp: CommonResponseWrapper<any>) => void,
                                callbackKo: (err: any) => void) {
        const payload = {
            filters: filters || new Map(),
            page: 1,
            pageSize: 100
        };

        this.http.post<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}/reload-filtered`,
            payload,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    saveProcessDetail(processId: string,
                      payload: any | any[],
                      callbackOk: (resp: CommonResponseWrapper<any>) => void,
                      callbackKo: (err: any) => void) {
        this.http.patch<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}/update-step`,
            payload,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, true, callbackKo);
    }

    processNextStep(processId: string,
                    callbackOk: (resp: CommonResponseWrapper<any>) => void,
                    callbackKo: (err: any) => void) {
        this.http.put<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}/next-step`,
            null,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, true, callbackKo);
    }

    processAbandon(processId: string,
                   callbackOk: (resp: CommonResponseWrapper<any>) => void,
                   callbackKo: (err: any) => void) {
        this.http.delete<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}`,
            {},
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, true, callbackKo);
    }

    processExport(processId: string,
                  callbackOk: (resp: CommonResponseWrapper<CommonFileDownloadWrapper>) => void,
                  callbackKo: (err: any) => void) {
        this.http.get<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}/export`,
            {},
            (resp: CommonResponseWrapper<CommonFileDownloadWrapper>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    processReplay(processId: string,
                  callbackOk: (resp: CommonResponseWrapper<any>) => void,
                  callbackKo: (err: any) => void) {
        this.http.put<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}/replay`,
            null,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, true, callbackKo);
    }

    processSetErrorStatus(processId: string,
                          callbackOk: (resp: CommonResponseWrapper<any>) => void,
                          callbackKo: (err: any) => void) {
        this.http.put<CommonResponseWrapper<any>>(`${URLS.PROCESS_BASE_URL}/${processId}/set-error`,
            null,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, true, callbackKo);
    }

    templateDownload(
        request: MassiveCreationAction_DownloadTemplate,
        mdDomain: string,
        callbackOk: (resp: CommonResponseWrapper<CommonFileDownloadWrapper>) => void,
        callbackKo: (err: any) => void
      ) {
        const params = {
          language: request.payload.language,
          mdDomain,
          client: request.payload.client,
          goldenRecord: request.payload.goldenRecord
        };

        this.http.get<CommonResponseWrapper<any>>(
          `${BASE_URL}/downloadTemplate`,
          params,
          (resp: CommonResponseWrapper<CommonFileDownloadWrapper>) => callbackOk(resp),
          {},
          false,
          callbackKo
        );
      }

    getMaterialDetails(materialFields: any,
                       callbackOk: (resp: CommonResponseWrapper<any>) => void,
                       callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<any>>(`${URLS.GET_MATERIAL_DETAILS}`,
            materialFields,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    loadMaterialView(requestBody: any,
                     callbackOk: (resp: CommonResponseWrapper<any>) => void,
                     callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<any>>(`${URLS.LOAD_MATERIAL_VIEW}`,
            requestBody,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    getMaterialErrors(materialId: string,
                      language: string,
                      callbackOk: (resp: CommonResponseWrapper<any>) => void,
                      callbackKo: (err: any) => void) {

        const param = {language};

        this.http.get<CommonResponseWrapper<any>>(`${URLS.GET_MATERIAL_ERRORS}/${materialId}/errors`,
            param,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }
}
