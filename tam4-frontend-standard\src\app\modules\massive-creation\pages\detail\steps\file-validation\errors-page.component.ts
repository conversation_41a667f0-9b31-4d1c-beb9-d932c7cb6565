import { CommonModule } from "@angular/common";
import { Component, input, output } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { DataViewModule } from "primeng/dataview";
import { DropdownModule } from "primeng/dropdown";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { SplitterModule } from "primeng/splitter";
import { TableModule } from "primeng/table";
import { ButtonModule } from "primeng/button";
import { MassiveCreationCommonsModule } from "src/app/modules/massive-creation/commons/massive-creation-commons.module";
import { EventUtils } from "src/app/utils";
import { MassiveCreationMaterialErrorDetailsComponent } from "../../massive-creation-material-error-details.component";
import { MassiveCreationService } from "src/app/modules/massive-creation/massive-creation.service";

@Component({
  selector: "div[errorsPage]",
  template: `
    @if (mode() == "BLOCKING_ERRORS") {
      <div class="flex flex-row justify-content-between align-items-end mb-3">
        <div class="flex flex-column w-15rem">
          <div class="filter-label">
            <em class="fas fa-filter"></em>
            <label>{{
              "massiveCreation.validatingData.errors.filter-materials" | translate
            }}</label>
          </div>
          <p-dropdown
            formControlName="selectedErrorType"
            styleClass="w-full"
            optionLabel="errorType"
            [showClear]="true"
            [options]="getFilters()"
            (onChange)="onErrorTypeChange($event)"
          >
          </p-dropdown>
        </div>
        <div class="flex flex-column">
          <p-button
            *ngIf="selectedMaterialsIds.length > 0"
            styleClass="p-button-danger"
            icon="pi pi-trash"
            [label]="'massiveCreation.delete.button' | translate"
            (onClick)="onDeleteSelected($event)"
          ></p-button>
        </div>
      </div>
    }
    <div class="flex flex-wrap w-100">
      <p-splitter
        class="w-100"
        [panelSizes]="[30, 70]"
        [style]="{ height: '650px' }"
        styleClass="w-100"
      >
        <ng-template pTemplate>
          <p-scrollPanel class="w-100" [style]="{ height: '620px', margin: '11px 0', width: '100%' }">
            <div class="p-4">
              <p-dataView [value]="getValue()">
                <ng-template let-items pTemplate="listItem">
                  @for (material of items; track material) {
                  <div class="group-container">
                    <div class="flex w-100 justify-content-between">
                      @if (mode() == "BLOCKING_ERRORS") {
                      <div
                        class="col-fixed px-3"
                        (click)="toggleErrors($event, material)"
                      >
                        <i class="pi" 
                        [class.pi-check-square]="selectedMaterialsIds.includes(material?.id)"
                        [class.pi-stop]="!selectedMaterialsIds.includes(material?.id)"
                        ></i>
                      </div>
                      }
                      <div
                        class="group-row flex-grow-1"
                        [class.bg-primary]="
                          currentSelectedId == material?.id &&
                          currentSelectedRow == material?.rowNumber"
                        (click)="toggleSelectRow($event, material)">
                        <div class="flex flex-column w-100 px-4 py-1">
                          <div class="flex flex-wrap justify-content-between align-items-center gap-2"
                          >
                            <div
                              tam-massive-creation-info-field
                              [inline]="true"
                              class="col-fixed m-0 p-0"
                              [label]="getRowNumberLabel()"
                              [value]="material.rowNumber"
                            ></div>
                          </div>
                          <div
                            class="flex flex-wrap justify-content-between gap-2 mt-2"
                          >
                            <div
                              tam-massive-creation-info-field
                              [inline]="true"
                              class="col-fixed m-0 p-0"
                              [label]="getDescriptionLabel()"
                              [value]="material.description"
                            ></div>
                          </div>
                          @if (mode() == "BLOCKING_ERRORS") {
                            <div
                              class="flex flex-wrap justify-content-between gap-2 mt-2"
                            >
                              <div
                                tam-massive-creation-info-field
                                [inline]="true"
                                class="col-fixed m-0 p-0"
                                [label]="getFieldNameLabel()"
                                [value]="material.fieldName"
                              ></div>
                            </div>
                            <div
                              class="flex flex-wrap justify-content-between gap-2 mt-2"
                            >
                              <div
                                tam-massive-creation-info-field
                                [inline]="true"
                                class="col-fixed m-0 p-0"
                                [label]="getErrorTypeLabel()"
                                [value]="material.errorType"
                              ></div>
                            </div>
                            <div
                              class="flex flex-wrap justify-content-between gap-2 mt-2"
                            >
                              <div
                                tam-massive-creation-info-field
                                [inline]="true"
                                class="col-fixed m-0 p-0"
                                [label]="getFieldValueLabel()"
                                [value]="material.fieldValue"
                              ></div>
                            </div>
                          } @else {
                            <div
                              class="flex flex-wrap justify-content-between gap-2 mt-2"
                            >
                              <div
                                tam-massive-creation-info-field
                                [inline]="true"
                                class="col-fixed m-0 p-0"
                                [label]="getErrorsOnRowLabel()"
                                [value]="material.errorType"
                              ></div>
                            </div>
                            <div
                              class="flex flex-wrap justify-content-between gap-2 mt-2"
                            >
                              <div
                                tam-massive-creation-info-field
                                [inline]="true"
                                class="col-fixed m-0 p-0"
                                [label]="getErrorsListLabel()"
                                [value]="material.fieldValue"
                              ></div>
                            </div>
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                  }
                </ng-template>
              </p-dataView>
            </div>
          </p-scrollPanel>
        </ng-template>
        <ng-template pTemplate>
          <p-scrollPanel class="w-100" [style]="{ height: '620px', margin: '11px 0', width: '100%' }">
            @if (currentSelectedRow == null) {
              {{'massiveCreation.validatingData.noData' | translate}}
            } @else {
              <div materialErrorDetails [fields]="getFields()"></div>
            }
          </p-scrollPanel>
        </ng-template>
      </p-splitter>
    </div>
  `,
  styles: [
    `
      .group-container {
        border-radius: 4px;
      }

      .group-row {
        display: flex;
        align-items: center;
        border-radius: 4px;
        cursor: pointer;
      }

      .group-row .flex-column {
        flex-grow: 1;
      }

      .group-row {
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
      }

      .col-fixed.px-3 {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
      }

      .col-fixed.px-3 i {
        font-size: 1.2rem;
      }
    `,
  ],
  standalone: true,
  imports: [
    CommonModule,
    MassiveCreationCommonsModule,
    MassiveCreationMaterialErrorDetailsComponent,
    DropdownModule,
    SplitterModule,
    ScrollPanelModule,
    DataViewModule,
    TableModule,
    ButtonModule,
    TranslateModule,
  ],
})
export class MassiveCreationErrorsPageComponent {

  form: FormGroup<{ selectedErrorType: FormControl<string | null> }>;
  mode = input.required<string>();
  errors = input.required<any[]>();
  filters = input.required<any[]>();

  errorTypeChange = output<any>(); // TODO: add TYPE
  deleteSelectedRows = output<number[]>();
  selectedMaterialsIds: string[];
  currentSelectedId: string = null;
  currentSelectedRow: string = null;

  constructor(private fb: FormBuilder, private translate: TranslateService, private service: MassiveCreationService) {
    this.form = this.fb.group({
      selectedErrorType: this.fb.control<string | null>(null),
    });
    this.selectedMaterialsIds = [];
  }

  getFilters() {
    return this.filters().flatMap(el =>
      Object.entries(el?.values ?? {}).map(([errorType, value]) => ({
        errorType,
        value
      }))
    );
  }

  getValue() {
    return this.errors().flatMap(item =>
      item?.massiveProcessFields.flatMap(field =>
        field?.errors.map(error => (
          {
            id: field.id,
            rowNumber: item.rowNumber,
            description: item.note,
            fieldName: field.fieldName,
            errorType: error.errorType,
            fieldValue: field.newValue,
      }
    ))));
  }

  getFields() {
    const selectedRow = this.errors().find(value => value.rowNumber == this.currentSelectedRow);
    
    return {
      rowNumber: selectedRow.rowNumber,
      fields: selectedRow.massiveProcessFields
    }
  }

  getRowNumberLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.rowNumberTitle"
    );
  }

  getDescriptionLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.descriptionTitle"
    );
  }

  getFieldNameLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.fieldNameTitle"
    );
  }

  getFieldValueLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.fieldValueTitle"
    );
  }

   getErrorsOnRowLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.errorsOnRow"
    );
  }

   getErrorsListLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.errorsList"
    );
  }

  getErrorTypeLabel(): string {
    return this.translate.instant(
      "massiveCreation.validatingData.errors.labels.errorType"
    );
  }

  toggleSelectRow(nativeEvent: any, data: any) {
    EventUtils.stopPropagation(nativeEvent);

    if ( data?.id !== this.currentSelectedId || data?.rowNumber !== this.currentSelectedRow ) {
      this.currentSelectedId = data.id;
      this.currentSelectedRow = data.rowNumber;
//       this.service.action_doGetMaterialErrors(data.id);
    } else {
      this.currentSelectedRow = null;
    }
  }

  toggleErrors(nativeEvent, data) {
          EventUtils.stopPropagation(nativeEvent);
  
          const dataId = data?.id;
  
          if (this.selectedMaterialsIds.includes(dataId)) {
              this.selectedMaterialsIds = this.selectedMaterialsIds.filter(id => id !== dataId);
          } else {
              this.selectedMaterialsIds.push(dataId);
          }
      }

  onErrorTypeChange(event: any) {
    this.errorTypeChange.emit(event?.value);
  }

  onDeleteSelected(event: any) {
    EventUtils.stopPropagation(event);

    if (this.selectedMaterialsIds.length === 0) {
      return;
    }
    // TODO: Optimize this!
    const rowIds = this.selectedMaterialsIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));
    if (rowIds.length > 0) {
      this.deleteSelectedRows.emit(rowIds);
    }
  }
}
