import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { MenuItem } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { take } from 'rxjs/operators';
import {
  CommonFileDownloadWrapper,
  CommonResponseWrapper,
  PagedResponseWrapper,
  ResponseOutcomeType,
  TableHeader,
  Tam4ComponentEvent,
} from '../../models';
import { HttpClientUtils, ObjectsUtils } from '../../utils';
import { CommonUtils } from '../../utils/common.utils';
import { MasterdataEnrichmentFieldStatusType } from '../massive-edit/models/massive-edit.types';
import {
    FieldDetailsRequest,
    MASSIVE_CREATION_CONST,
    MassiveCreationStatusType,
    MassiveCreationStepEventType,
    MassiveCreationUtils, MassiveProcessCreationData,
    MaterialDetailsRequest,
    SearchEvents
} from './models/massive-creation.types';
import { MassiveCreationListProcessCreatedDialog } from './pages/list/massive-creation-list.process-created.dialog';
import { MASSIVE_CREATION_ACTIONS } from './store/massive-creation.actions';
import { MassiveCreationState } from './store/massive-creation.state';

@Injectable({
    providedIn: 'root'
})
export class MassiveCreationService {

    that: MassiveCreationService;

    constructor(private store: Store<MassiveCreationState>,
                private router: Router,
                private dialogService: DialogService) {

        this.that = this;
    }

    init_list() {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_INIT({}));
    }

    action_doSearchSuccess() {
        return (resp: PagedResponseWrapper<any>): void => {
            if (ObjectsUtils.isNoU(resp)) {
                const out = HttpClientUtils.getErrorWrapper(
                    'massiveCreation.list.errors.wrong-payload.title',
                    'massiveCreation.list.errors.wrong-payload.messageBody');
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_FAILURE(out));
                return;
            } else if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_FAILURE(resp));
                return;
            }
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_SUCCESS(resp));
        };
    }

    action_doSearchFailure() {
        return (resp: PagedResponseWrapper<any>): void => {
        };
    }

    action_doSearchInitSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (ObjectsUtils.isNoU(resp)) {
                const out = HttpClientUtils.getErrorWrapper(
                    'massiveCreation.list.errors.wrong-payload.title',
                    'massiveCreation.list.errors.wrong-payload.messageBody');
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_INIT_FAILURE(out));
                return;
            } else if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_INIT_FAILURE(resp));
                return;
            }
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.SET_USER_ACTIONS(resp.data?.userActions));
            const listFilters: CommonResponseWrapper<any> = {data: resp.data?.listFilters, messages: resp.messages, outcome: resp.outcome};
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_INIT_SUCCESS(listFilters));
        };
    }

    action_doSearchInitFailure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH_INIT_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doSearch(event: Tam4ComponentEvent<SearchEvents, any>) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.SEARCH(event?.payload));
    }

    action_doStartProcess(event: File[]) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.NEW_PROCESS_SUBMIT(event));
    }

    action_doStartProcessSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.NEW_PROCESS_SUBMIT_FAILURE(resp, true));
            } else {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.NEW_PROCESS_SUBMIT_SUCCESS(resp));
                const ref = this.dialogService.open(MassiveCreationListProcessCreatedDialog,
                    {
                        showHeader: false,
                        data: resp,
                        width: '50vw',
                        closable: false,
                        dismissableMask: false,
                        position: 'top'
                    });
                ref.onClose.pipe(take(1)).subscribe(x => {
                    if (ObjectsUtils.isNotNoU(x)) {
                        this.gotoDetail({processId: x});
                    }

                    ref.destroy();
                });
            }
        };
    }

    action_doStartProcessFailure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.NEW_PROCESS_SUBMIT_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
    }

    action_doProcessClosed() {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.NEW_PROCESS_CLOSED());
    }

    action_doProcessDetailLoad(processId: any, payload?: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_LOAD({processId}));
    }

    action_doProcessDetailReload(processId: any, payload?: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_RELOAD({processId}));
    }

    action_doProcessDetailReloadFiltered(processId: any, filters?: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_RELOAD_FILTERED({processId, data: filters}));
    }

    action_doProcessDetailReloadFilteredSuccess(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            const response = ObjectsUtils.deepClone(resp?.data);
            if (response?.data) {
                response.data.stepInfomration = MassiveCreationUtils.getStepDetail(response?.data?.stepInfomration);
            }
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_RELOAD_FILTERED_SUCCESS({
                processId,
                data: {...resp}
            }));
        };
    }

    action_doProcessDetailReloadFilteredFailure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_RELOAD_FILTERED_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessDetailLoadSuccess(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            const response = ObjectsUtils.deepClone(resp?.data);
            if (response?.data) {
                response.data.stepInfomration = MassiveCreationUtils.getStepDetail(response?.data?.stepInfomration);
            }
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_LOAD_SUCCESS({
                processId,
                data: {...resp}
            }));
        };
    }

    action_doProcessDetailLoadFailure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_LOAD_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessStep_Save(processId: any, payload?: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_SAVE({processId, data: payload}));
    }

    action_doProcessStep_Save_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_SAVE_SUCCESS({processId, data: resp}));
        };
    }

    action_doProcessStep_Save_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_SAVE_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessStep_Next(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_NEXTSTEP({processId, data: payload}));
    }

    action_doProcessStep_Next_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_NEXTSTEP_SUCCESS({
                processId,
                data: resp
            }));
        };
    }

    action_doProcessStep_Next_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_NEXTSTEP_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessStep_Abandon(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_ABANDON({processId, data: payload}));
    }

    action_doProcessStep_Abandon_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_ABANDON_SUCCESS({processId, data: resp}));
        };
    }

    action_doProcessStep_Abandon_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_ABANDON_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doOpenMaterialDetails(materialCode: string, client: string) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.OPEN_MATERIAL_DETAILS({materialCode, client}));
    }
    action_doProcessStep_Export(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_EXPORT({processId, data: payload}));
    }

    action_doProcessStep_Export_Success(processId: any) {
        return (resp: CommonResponseWrapper<CommonFileDownloadWrapper>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_EXPORT_FAILURE(resp, true));
            } else {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_EXPORT_SUCCESS({processId}));
                CommonUtils.saveFile(resp?.data);
            }


        };
    }

    action_doProcessStep_Export_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_EXPORT_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessStep_Validate(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_VALIDATE({processId, data: payload}));
    }

    action_doProcessStep_Validate_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_VALIDATE_SUCCESS({
                processId,
                data: resp
            }));
        };
    }

    action_doProcessStep_Validate_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_VALIDATE_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doStepDataChanged(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_CHANGED(payload));
    }

    action_doProcessStep_Replay(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_REPLAY({processId, data: payload}));
    }

    action_doProcessStep_Replay_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_REPLAY_SUCCESS({
                processId,
                data: resp
            }));
        };
    }

    action_doProcessStep_Replay_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_REPLAY_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessStep_Set_Error(processId: any, payload: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_SET_ERROR({processId, data: payload}));
    }

    action_doProcessStep_Set_Error_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_SET_ERROR_SUCCESS({
                processId,
                data: resp
            }));
        };
    }

    action_doProcessStep_Set_Error_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_SET_ERROR_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doProcessStep_Delete(processId: any, rowIds: MassiveProcessCreationData[]) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_DELETE({processId, data: rowIds}));
    }

    action_doProcessStep_Delete_Success(processId: any) {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_DELETE_SUCCESS({processId, data: resp}));
        };
    }

    action_doProcessStep_Delete_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.PROCESS_DETAIL_DELETE_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_doDownloadTemplate(request: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.DOWNLOAD_TEMPLATE(request));
    }

    action_doDownloadTemplate_Success() {
        return (resp: CommonResponseWrapper<CommonFileDownloadWrapper>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.DOWNLOAD_TEMPLATE_FAILURE(resp, true));
            } else {
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.DOWNLOAD_TEMPLATE_SUCCESS());
                CommonUtils.saveFile(resp?.data);
            }
        };
    }

    action_doDownloadTemplate_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.DOWNLOAD_TEMPLATE_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    action_OpenDownloadTemplate() {
      this.store.dispatch(MASSIVE_CREATION_ACTIONS.OPEN_DOWNLOAD_TEMPLATE());
    }

    action_doGetMaterialDetails(materialFields: any) {
        this.store.dispatch(MASSIVE_CREATION_ACTIONS.GET_MATERIAL_DETAILS(materialFields));
    }

    action_doGetMaterialDetails_Success() {
        return (resp: CommonResponseWrapper<any>): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.GET_MATERIAL_DETAILS_SUCCESS(resp));
        };
    }

    action_doGetMaterialDetails_Failure() {
        return (err): void => {
            this.store.dispatch(MASSIVE_CREATION_ACTIONS.GET_MATERIAL_DETAILS_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    getFieldDetailsRequest(
        fields: any,
        language: string,
        fallbackLanguages: string[]
    ): FieldDetailsRequest {
        return {
            fields,
            language,
            fallbackLanguages
        };
    }

    getMaterialDetailsRequest(
        data: any,
        language: string,
        fallbackLanguages: string[]
    ): MaterialDetailsRequest {
        return {
            materialCode: data.materialCode,
            client: data.client,
            language,
            fallbackLanguages,
            page: 'ItemDetails'
        };
    }

    loadTableConfig(): TableHeader[] {
        return MASSIVE_CREATION_CONST.tblHeaders;
    }

    doListEvents(event: Tam4ComponentEvent<SearchEvents, any>, formValues?: any) {
        switch (event.type) {
            case SearchEvents.NEW_PROCESS:
                this.store.dispatch(MASSIVE_CREATION_ACTIONS.NEW_PROCESS());
                break;
            case SearchEvents.SEARCH:
                this.action_doSearch({...event, payload: formValues});
                break;
            case SearchEvents.TOGGLE_REFRESH:
                break;
            case SearchEvents.OPEN_DOWNLOAD_TEMPLATE:
                this.action_OpenDownloadTemplate();
                break;
            default:
                break;
        }
    }

    getCurrentStatusIdx(procInfo: any, menu: MenuItem[]): number {
        if (ObjectsUtils.isNoU(procInfo) || menu?.length < 1) {
            return 0;
        }
        return menu.findIndex(i => i.id === this.normalizeStatus(procInfo.currentStatus));
    }

    normalizeStatus(status: string): string {
        switch (status) {
            case 'A_CREATION_STARTING':
            case 'A_CREATION_FORMAL_FILE_VALIDATION':
            case 'H_CREATION_FILE_VALIDATION':
            case 'A_CREATION_VALIDATING_CATEGORY':
            case 'A_CREATION_VALIDATING_CATEGORY_SUMMARY':
            case 'A_CREATION_VALIDATING_BASIC_DATA':
            case 'A_CREATION_VALIDATING_BASIC_DATA_SUMMARY':
            case 'A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES':
            case 'A_CREATION_VALIDATING_TECHNICAL_ATTRIBUTES_SUMMARY':
            case 'A_CREATION_VALIDATING_PLANTS':
            case 'A_CREATION_VALIDATING_PLANTS_SUMMARY':
            case 'A_CREATION_EXTRACTING_DATA':
            case 'A_CREATION_VALIDATING_DATA':
            case 'H_CREATION_CONFIRM_DATA':
            case 'A_CREATION_PROCESS_GENERATION':
                return status;
            case 'H_CREATION_COMPLETED':
            case 'H_CREATION_ABANDONED':
            case 'H_CREATION_TERMINATED':
            default:
                return 'END';
        }
    }

    gotoDetail(process: any) {
        this.router.navigate(['app', 'massive-creation', 'process', process?.processId]);
    }

    gotoList() {
        this.router.navigate(['app', 'massive-creation', 'list']);
    }

    doWorkareaEvent(processId: number, event: Tam4ComponentEvent<MassiveCreationStepEventType, any>) {
        this.launchStepFunction(processId, event);
    }

    getOnlyMeaningfullFields(materialList: any): any[] {

        const actionPayload = [];
        if (Array.isArray(materialList)) {
            materialList?.forEach(m => {
                    if (m?.status === MassiveCreationStatusType.CONFIRMED
                        || m?.status === MassiveCreationStatusType.VALIDATED
                        || m?.status === MassiveCreationStatusType.IGNORED) {
                            let adjustedStatus = m?.status;

                            if (m?.status === MasterdataEnrichmentFieldStatusType.CONFIRMED) {
                                adjustedStatus = MasterdataEnrichmentFieldStatusType.IGNORED;
                            } else if (m?.status === MasterdataEnrichmentFieldStatusType.IGNORED) {
                                adjustedStatus = MasterdataEnrichmentFieldStatusType.CONFIRMED;
                            }
                            actionPayload.push({
                                groupId: m?.groupId,
                                status: adjustedStatus
                            });
                    }
            });
        }

        return actionPayload;
    }

    launchStepFunction(processId: number, event: Tam4ComponentEvent<MassiveCreationStepEventType, any>) {

        switch (event?.type) {
            case MassiveCreationStepEventType.SAVE:
                this.action_doProcessStep_Save(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.CHANGED:
                this.action_doStepDataChanged(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.NEXT:
                this.action_doProcessStep_Next(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.EXPORT:
                this.action_doProcessStep_Export(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.ABANDON:
                this.action_doProcessStep_Abandon(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.VALIDATE:
                this.action_doProcessStep_Validate(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.REFRESH:
                this.action_doProcessDetailReload(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.RELOAD_FILTERED:
                this.action_doProcessDetailReloadFiltered(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.REPLAY:
                this.action_doProcessStep_Replay(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.SET_ERROR:
                this.action_doProcessStep_Set_Error(processId, event?.payload);
                break;
            case MassiveCreationStepEventType.DELETE:
                this.action_doProcessStep_Delete(processId, event?.payload);
                break;
            default:
                console.log(`function not valid ${event?.type}-${event?.subtype}`);
                break;
        }

    }
}
