import {Component, computed, inject, OnInit, signal} from "@angular/core";
import { MassiveCreationDetailAbstractStep } from "../massive-creation-detail.abstract.step";
import { CommonModule } from "@angular/common";
import { MassiveCreationNavigationHeaderComponent } from "../../../navigation-header/navigation-header.component";
import { MassiveCreationErrorsPageComponent } from "./errors-page.component";
import { CriticalErrorsComponent } from "./critical-errors.component";
import {
    NavigationTab,
    MassiveCreationStepEventType,
    MassiveProcessCreationData
} from "src/app/modules/massive-creation/models/massive-creation.types";
import {BehaviorSubject} from "rxjs";
import {COMMON_DEFAULT_CFG, MassiveCreationDetailStepActionBarConfig, MassiveCreationDetailStepActionBarComponent} from "../massive-creation-detail.step-action-bar.component";
import { ConfirmationService } from "primeng/api";
import { TranslateService } from "@ngx-translate/core";
import { EventUtils } from "src/app/utils";
import {InternalCommonModule} from "../../../../../common/iternal-common.module";

const DEFAULT_STEP_ACTION_CFG: MassiveCreationDetailStepActionBarConfig = {
    ...COMMON_DEFAULT_CFG,
    showNext: true,
    showSave: false,
    showAbandon: true,
    showReload: false,
    labelNext: 'massiveCreation.processDetail.actions.H_CREATION_FILE_VALIDATION.next',
};

@Component({
  template: `
    @if (isCriticalErrors()) {
      <div criticalErrors></div>
    } @else {
      <div
        navigationHeader
        [tabs]="errorTabs()"
        [activeTab]="activeTab()"
        [loading]="loadingIndicators()"
        (changeTab)="changeTab($event)"
      ></div>


      <div errorsPage [mode]="activeTab()" [errors]="stepData.data" [filters]="stepData.filterTypes" (errorTypeChange)="errorTypeChange($event)" (deleteSelectedRows)="deleteRows($event)"></div>
    }
    <tam4-massive-creation-detail-step-action-bar
            (nextEvent)="doNext($event, stepData)"
            (abandonEvent)="doAbandon($event, stepData)"
            (reloadEvent)="doReload($event, stepData)"
            [pageDirty]="changed"
            [loading]="loading"
            [config]="compConfig | async"
    >
    </tam4-massive-creation-detail-step-action-bar>

    <p-confirmDialog styleClass="w-50vw-fixed" #deleteConfirmDialog key="deleteConfirmDialog">
      <ng-template pTemplate="headless" let-message>
        <div class="flex flex-column align-items-center p-5 surface-overlay border-round">
          <div class="border-circle bg-primary inline-flex justify-content-center align-items-center h-4rem w-4rem">
            <i [class]="message.icon" class="fa-2xl"></i>
          </div>
          <span class="font-bold text-2xl block mb-2 mt-4">{{ message.header | translate }}</span>
          <div class="mb-0" [innerHTML]="message.message | translate | safeHtml"></div>
          <div class="flex align-items-center gap-2 mt-4">
            <button pButton [icon]="message?.rejectIcon" [label]="message?.rejectLabel | translate"
                    (click)="deleteConfirmDialog.reject()"
                    class="p-button-outlined ellipsis p-button-fixed-size">
            </button>
            <button pButton [icon]="message?.acceptIcon" [label]="message?.acceptLabel | translate"
                    (click)="deleteConfirmDialog.accept()"
                    class="ellipsis p-button-fixed-size">
            </button>
          </div>
        </div>
      </ng-template>
    </p-confirmDialog>
  `,
  standalone: true,
    imports: [
        CommonModule,
        InternalCommonModule,
        CriticalErrorsComponent,
        MassiveCreationNavigationHeaderComponent,
        MassiveCreationErrorsPageComponent,
        MassiveCreationDetailStepActionBarComponent
    ],
    providers: [ConfirmationService],
})
export class MassiveCreationDetail_H_CREATION_FILE_VALIDATION_Step
  extends MassiveCreationDetailAbstractStep
  implements OnInit
{

  compConfig: BehaviorSubject<MassiveCreationDetailStepActionBarConfig> =
    new BehaviorSubject<MassiveCreationDetailStepActionBarConfig>({
      ...DEFAULT_STEP_ACTION_CFG
  });

  navigationTabsSig = signal<NavigationTab[]>([]);
  activeTab = signal("BLOCKING_ERRORS"); // REFACTOR THIS
  loadingIndicators = signal(false);
  isCriticalErrors = signal(false);
  errorTabs = computed(() =>
    (this.navigationTabsSig() ?? [])
        .map<NavigationTab>((t) => ({
            type: t.type, // REFACTOR
            key: t.key,
            value: t.value,
            active: t.active,
      }))
  );

  confirmationService: ConfirmationService = inject(ConfirmationService);
  translate: TranslateService = inject(TranslateService);

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.navigationTabsSig.set(this.navigationTabs ?? []);
  }

  changeTab(event: any) {
    this.activeTab.set(event);
    this.doReloadFiltered(null, { tabFilter: event });
  }

  errorTypeChange(event: any) {
    this.doReloadFiltered(null, { errorTypeFilter: event });
  }

  deleteRows(rowIds: number[]): void {
    if (!rowIds || rowIds.length === 0) {
      return;
    }

    // TODO: Optimize the payload creation
    const payload: MassiveProcessCreationData[] = this.stepData?.data.filter(d => rowIds.includes(d.id));
    payload.forEach((p: any) => p.status = "DELETED");
    console.log('Request to delete rows:', this.stepData.data, rowIds, payload);

    const confirmMessage = this.translate.instant('massiveCreation.delete.confirmMessage', { count: rowIds.length });
    const confirmTitle = this.translate.instant('massiveCreation.delete.confirmTitle');

    this.confirmationService.confirm({
      key: 'deleteConfirmDialog',
      header: confirmTitle,
      message: confirmMessage,
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: 'fa-solid fa-check',
      rejectIcon: 'fa-solid fa-x',
      acceptLabel: this.translate.instant('massiveCreation.delete.confirmAccept'),
      rejectLabel: this.translate.instant('massiveCreation.delete.confirmReject'),
      rejectButtonStyleClass: 'p-button-text',
      accept: () => {
        EventUtils.stopPropagation(null);
        this.stepEvent.emit({
          type: MassiveCreationStepEventType.DELETE,
          subtype: this.stepType,
          nativeEvent: null,
          payload
        });
      }
    });
  }

}
