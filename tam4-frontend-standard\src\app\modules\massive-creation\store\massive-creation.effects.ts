import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { BsModalService } from "ngx-bootstrap/modal";
import { MessageService } from 'primeng/api';
import { filter, map, tap, withLatestFrom } from 'rxjs/operators';
import { GenericSearchForm, Tam4Action, Tam4BaseConstants } from '../../../models';
import { FormUtils, ObjectsUtils, ReduxUtils } from '../../../utils';
import { getCurrentLanguage } from '../../bulk-upload/store/profile/profile.state';
import { getFallbackLanguages } from '../../layout/store/reducers';
import { ViewModeEnum } from '../../materials-editor/models/material-editor.types';
import { MaterialsModalService } from '../../materials-modal/materials-modal.service';
import { GlobalFilterValues } from "../../materials/store/global-filter/global-filter.state";
import { getCurrentValues } from "../../worklists/store/global-filter/global-filter.state";
import { MassiveCreationDao } from '../massive-creation.dao';
import { MassiveCreationService } from '../massive-creation.service';
import { MassiveCreationTemplateDownloadComponent } from "../pages/detail/template-download/massive-creation-template-download";
import {
  MASSIVE_CREATION_ACTION_NAMES,
  MassiveCreationAction_DownloadTemplate,
  MassiveCreationAction_NewProcess_Submit,
  MassiveCreationAction_ProcessDetail_Abandon,
  MassiveCreationAction_ProcessDetail_Delete,
  MassiveCreationAction_ProcessDetail_Export,
  MassiveCreationAction_ProcessDetail_Load,
  MassiveCreationAction_ProcessDetail_Reload_Filtered,
  MassiveCreationAction_ProcessDetail_Save,
  MassiveCreationAction_ProcessDetail_Validate,
  MassiveCreationAction_Search,
  MassiveCreationActionTypes
} from './massive-creation.actions';
import { MassiveCreationSelectors } from './massive-creation.selectors';
import { MassiveCreationSearchForm, MassiveCreationState } from './massive-creation.state';


@Injectable({providedIn: 'root'})
export class MassiveCreationEffects {

    constructor(private actions$: Actions<MassiveCreationActionTypes>,
                private translate: TranslateService,
                private toastService: MessageService,
                private service: MassiveCreationService,
                private materialModalService: MaterialsModalService,
                private dao: MassiveCreationDao,
                private store: Store<MassiveCreationState>,
                private modalService: BsModalService
    ) {

    }

    onSearchEffect = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.SEARCH),
        tap(x => {
        }),
        withLatestFrom(
            this.store.select(MassiveCreationSelectors.getSearchFormConfig)
        ),
        map((i: [MassiveCreationAction_Search, MassiveCreationSearchForm]): GenericSearchForm => {
            const a$: MassiveCreationAction_Search = i[0];
            const formConfig: MassiveCreationSearchForm = i[1];

            return FormUtils.applyValueToConfig(a$.payload, formConfig);
        }),
        map(x => FormUtils.removeVerboseInfoFromForm(x, true)),
        tap((requestBody: GenericSearchForm) => {
            this.dao.search(requestBody,
                this.service.action_doSearchSuccess(),
                this.service.action_doSearchFailure());
        })
    ), ReduxUtils.noDispatch());

    onSearchInitEffect = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.SEARCH_INIT),
        tap(a$ => {
            const action = ObjectsUtils.forceCast<MassiveCreationState>(a$);
            this.dao.searchInit(
                this.service.action_doSearchInitSuccess(),
                this.service.action_doSearchInitFailure());
        })
    ), ReduxUtils.noDispatch());


    onNewProcessSubmitEffect = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.NEW_PROCESS_SUBMIT),
        withLatestFrom(this.store.select(getCurrentLanguage)),
        tap((i: [MassiveCreationAction_NewProcess_Submit, string]) => {

            for (const file of i[0]?.payload) {
                this.dao.startProcess(
                    i[1],
                    file,
                    this.service.action_doStartProcessSuccess(),
                    this.service.action_doStartProcessFailure());
            }

        })
    ), ReduxUtils.noDispatch());

    onProcessDetailLoad = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_LOAD,
            MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD,
            MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE_SUCCESS,
            MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_NEXTSTEP_SUCCESS,
            MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_ABANDON_SUCCESS),
        tap((a$: MassiveCreationAction_ProcessDetail_Load) => {
            this.dao.loadProcessDetail(a$.payload?.processId,
                this.service.action_doProcessDetailLoadSuccess(a$.payload?.processId),
                this.service.action_doProcessDetailLoadFailure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailReloadFiltered = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_RELOAD_FILTERED),
        tap((a$: MassiveCreationAction_ProcessDetail_Reload_Filtered) => {
            this.dao.reloadFilteredProcessDetail(a$.payload?.processId, a$.payload?.data,
                this.service.action_doProcessDetailReloadFilteredSuccess(a$.payload?.processId),
                this.service.action_doProcessDetailReloadFilteredFailure());
        })
    ), ReduxUtils.noDispatch());


    onProcessDetailSave = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SAVE),
        tap((a$: any) => {
            const requestBody = this.service.getOnlyMeaningfullFields(a$.payload?.data);

            this.dao.saveProcessDetail(a$.payload?.processId, requestBody,
                this.service.action_doProcessStep_Save_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Save_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailNext = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_NEXTSTEP),
        tap((a$: MassiveCreationAction_ProcessDetail_Save) => {
            this.dao.processNextStep(a$.payload?.processId,
                this.service.action_doProcessStep_Next_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Next_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailAbandon = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_ABANDON),
        tap((a$: MassiveCreationAction_ProcessDetail_Abandon) => {
            this.dao.processAbandon(a$.payload?.processId,
                this.service.action_doProcessStep_Abandon_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Abandon_Failure());
        })
    ), ReduxUtils.noDispatch());


    onProcessDetailExport = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_EXPORT),
        tap((a$: MassiveCreationAction_ProcessDetail_Export) => {
            this.dao.processExport(a$.payload?.processId,
                this.service.action_doProcessStep_Export_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Export_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailValidate = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_VALIDATE),
        tap((a$: MassiveCreationAction_ProcessDetail_Validate) => {
            this.dao.processAbandon(a$.payload?.processId,
                this.service.action_doProcessStep_Validate_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Validate_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailReplay = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_REPLAY),
        tap((a$: MassiveCreationAction_ProcessDetail_Save) => {
            this.dao.processReplay(a$.payload?.processId,
                this.service.action_doProcessStep_Replay_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Replay_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailSetError = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_SET_ERROR),
        tap((a$: MassiveCreationAction_ProcessDetail_Save) => {
            this.dao.processSetErrorStatus(a$.payload?.processId,
                this.service.action_doProcessStep_Set_Error_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Set_Error_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailDelete = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_DELETE),
        tap((a$: MassiveCreationAction_ProcessDetail_Delete) => {
            this.dao.saveProcessDetail(a$.payload?.processId, a$.payload?.data,
                this.service.action_doProcessStep_Delete_Success(a$.payload?.processId),
                this.service.action_doProcessStep_Delete_Failure());
        })
    ), ReduxUtils.noDispatch());

    onProcessDetailDeleteSuccess = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.PROCESS_DETAIL_DELETE_SUCCESS),
        withLatestFrom(this.store.select(getCurrentValues)),
        tap(([a$, currentFilters]: [any, any]) => {
            this.toastService.add({
                ...Tam4BaseConstants.default_success_toast_options,
                summary: this.translate.instant('massiveCreation.delete.success.title'),
                detail: this.translate.instant('massiveCreation.delete.success.message')
            });
            this.dao.reloadFilteredProcessDetail(a$.payload?.processId, currentFilters,
                this.service.action_doProcessDetailReloadFilteredSuccess(a$.payload?.processId),
                this.service.action_doProcessDetailReloadFilteredFailure());
        })
    ), ReduxUtils.noDispatch());

    genericFailure = createEffect(() => this.actions$.pipe(
        filter(type => `${type?.type}`.includes('[FAILURE]')),
        tap((a$: MassiveCreationActionTypes) => {
            const act: Tam4Action<any> = ObjectsUtils.forceCast<Tam4Action<any>>(a$);

            if (act.manualEvent) {
            } else {
                this.toastService.add({
                    ...Tam4BaseConstants.default_error_toast_options,
                    summary: 'generic.messages.error.title',
                    detail: 'generic.messages.error.body'
                });
            }
        })
    ), ReduxUtils.noDispatch());

    onTemplateDownload = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.DOWNLOAD_TEMPLATE),
        withLatestFrom(this.store.select(getCurrentValues)),
        tap(([action, filters]: [MassiveCreationAction_DownloadTemplate, GlobalFilterValues]) => {
            this.dao.templateDownload(
                action,
                filters.mdDomain,
                this.service.action_doDownloadTemplate_Success(),
                this.service.action_doDownloadTemplate_Failure()
            );
        })
    ), ReduxUtils.noDispatch());

    onOpenDownloadTemplate = createEffect(() =>
      this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.OPEN_DOWNLOAD_TEMPLATE),
        tap(action => {
          // Apertura modal
          this.modalService.show(MassiveCreationTemplateDownloadComponent, {
            ignoreBackdropClick: true,
            backdrop: 'static',
            class: 'modal-dialog modal-dialog-centered'
          });
        })
      ),
      { dispatch: false }
    );

    onGetMaterialDetails = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.GET_MATERIAL_DETAILS),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)),
        tap((i: [any, string, string[]]) => {
            this.dao.getMaterialDetails(this.service.getFieldDetailsRequest(i[0].payload, i[1], i[2]),
                this.service.action_doGetMaterialDetails_Success(),
                this.service.action_doGetMaterialDetails_Failure());
        })
    ), ReduxUtils.noDispatch());

    onOpenMaterialDetails = createEffect(() => this.actions$.pipe(
        ofType(MASSIVE_CREATION_ACTION_NAMES.OPEN_MATERIAL_DETAILS),
        withLatestFrom(this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)),
        tap((i: [any, string, string[]]) => {
            this.materialModalService.openMaterialModal({}, ViewModeEnum.DETAILS);
            this.dao.loadMaterialView(this.service.getMaterialDetailsRequest(i[0].payload, i[1], i[2]),
                this.materialModalService.action_doLoadMaterialViewSuccess(),
                this.materialModalService.action_doLoadMaterialViewFailure());
        })
    ), ReduxUtils.noDispatch());

}
